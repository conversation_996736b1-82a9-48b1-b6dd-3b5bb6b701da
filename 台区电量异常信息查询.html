<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>台区电量异常信息查询系统</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-bolt text-primary text-3xl"></i>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年8月4日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <!-- 查询条件面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <h2 class="text-xl font-semibold text-text-primary mb-4">台区电量异常信息查询</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">管理单位</label>
          <div class="relative">
            <select id="management-unit" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部单位</option>
              <option value="薛家湾供电公司">薛家湾供电公司</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">台区编号</label>
          <input type="text" id="station-code" placeholder="请输入台区编号" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">台区名称</label>
          <input type="text" id="station-name" placeholder="请输入台区名称" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">异常类型</label>
          <div class="relative">
            <select id="exception-type" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部类型</option>
              <option value="电压异常">电压异常</option>
              <option value="电流异常">电流异常</option>
              <option value="功率异常">功率异常</option>
              <option value="通信异常">通信异常</option>
              <option value="设备故障">设备故障</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">开始日期</label>
          <input type="date" id="start-date" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">结束日期</label>
          <input type="date" id="end-date" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>
      </div>

      <div class="flex justify-end space-x-4 mt-6">
        <button id="btn-reset" class="px-6 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
          <i class="fa fa-refresh mr-1"></i> 重置
        </button>
        <button id="btn-search" class="px-6 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
          <i class="fa fa-search mr-1"></i> 查询
        </button>
        <button id="btn-export" class="px-6 py-3 bg-secondary text-white rounded-lg border border-secondary hover:bg-secondary/90 transition-colors">
          <i class="fa fa-download mr-1"></i> 导出
        </button>
      </div>
    </div>

    <!-- 数据统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">异常总数</p>
            <h3 class="text-3xl font-bold mt-2">5 <span class="text-lg font-normal text-text-secondary">条</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-danger/10 flex items-center justify-center text-danger">
            <i class="fa fa-exclamation-triangle text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">电压异常</p>
            <h3 class="text-3xl font-bold mt-2">2 <span class="text-lg font-normal text-text-secondary">条</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-warning/10 flex items-center justify-center text-warning">
            <i class="fa fa-bolt text-xl"></i>
          </div>
        </div>
      </div>

    <!-- 数据表格 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">台区电量异常信息列表</h3>
        <span class="text-text-secondary">共 5 条记录</span>
      </div>

      <div class="overflow-x-auto scrollbar-thin">
        <table class="w-full border-collapse border border-border">
          <thead>
            <tr class="bg-light">
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">管理单位</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">台区编号</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">台区名称</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">线路编号</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">线路名称</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">装机容量(kW)</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">异常类型</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">异常发生时间</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">异常恢复时间</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">异常持续时间</th>
            </tr>
          </thead>
          <tbody id="data-table-body">
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 text-text-primary">薛家湾供电公司</td>
              <td class="border border-border px-4 py-3 text-text-primary">TQ001</td>
              <td class="border border-border px-4 py-3 text-text-primary">赛罕一台区</td>
              <td class="border border-border px-4 py-3 text-text-primary">XL001</td>
              <td class="border border-border px-4 py-3 text-text-primary">赛罕主线</td>
              <td class="border border-border px-4 py-3 text-text-primary">500</td>
              <td class="border border-border px-4 py-3">
                <span class="px-2 py-1 bg-warning/20 text-warning rounded-full text-sm">电压异常</span>
              </td>
              <td class="border border-border px-4 py-3 text-text-primary">2025-08-04 08:30:15</td>
              <td class="border border-border px-4 py-3 text-text-primary">2025-08-04 10:15:30</td>
              <td class="border border-border px-4 py-3 text-text-primary">1小时45分钟</td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 text-text-primary">薛家湾供电公司</td>
              <td class="border border-border px-4 py-3 text-text-primary">TQ002</td>
              <td class="border border-border px-4 py-3 text-text-primary">九原二台区</td>
              <td class="border border-border px-4 py-3 text-text-primary">XL002</td>
              <td class="border border-border px-4 py-3 text-text-primary">九原支线</td>
              <td class="border border-border px-4 py-3 text-text-primary">320</td>
              <td class="border border-border px-4 py-3">
                <span class="px-2 py-1 bg-danger/20 text-danger rounded-full text-sm">设备故障</span>
              </td>
              <td class="border border-border px-4 py-3 text-text-primary">2025-08-04 14:20:45</td>
              <td class="border border-border px-4 py-3 text-text-primary">2025-08-04 16:45:20</td>
              <td class="border border-border px-4 py-3 text-text-primary">2小时24分钟</td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 text-text-primary">薛家湾供电公司</td>
              <td class="border border-border px-4 py-3 text-text-primary">TQ003</td>
              <td class="border border-border px-4 py-3 text-text-primary">康巴什三台区</td>
              <td class="border border-border px-4 py-3 text-text-primary">XL003</td>
              <td class="border border-border px-4 py-3 text-text-primary">康巴什环线</td>
              <td class="border border-border px-4 py-3 text-text-primary">750</td>
              <td class="border border-border px-4 py-3">
                <span class="px-2 py-1 bg-secondary/20 text-secondary rounded-full text-sm">通信异常</span>
              </td>
              <td class="border border-border px-4 py-3 text-text-primary">2025-08-03 22:15:30</td>
              <td class="border border-border px-4 py-3 text-text-primary">2025-08-04 06:30:15</td>
              <td class="border border-border px-4 py-3 text-text-primary">8小时14分钟</td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 text-text-primary">薛家湾供电公司</td>
              <td class="border border-border px-4 py-3 text-text-primary">TQ004</td>
              <td class="border border-border px-4 py-3 text-text-primary">集宁四台区</td>
              <td class="border border-border px-4 py-3 text-text-primary">XL004</td>
              <td class="border border-border px-4 py-3 text-text-primary">集宁北线</td>
              <td class="border border-border px-4 py-3 text-text-primary">420</td>
              <td class="border border-border px-4 py-3">
                <span class="px-2 py-1 bg-warning/20 text-warning rounded-full text-sm">电压异常</span>
              </td>
              <td class="border border-border px-4 py-3 text-text-primary">2025-08-03 16:45:20</td>
              <td class="border border-border px-4 py-3 text-text-primary">2025-08-03 18:20:10</td>
              <td class="border border-border px-4 py-3 text-text-primary">1小时34分钟</td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 text-text-primary">薛家湾供电公司</td>
              <td class="border border-border px-4 py-3 text-text-primary">TQ005</td>
              <td class="border border-border px-4 py-3 text-text-primary">东胜五台区</td>
              <td class="border border-border px-4 py-3 text-text-primary">XL005</td>
              <td class="border border-border px-4 py-3 text-text-primary">东胜工业线</td>
              <td class="border border-border px-4 py-3 text-text-primary">680</td>
              <td class="border border-border px-4 py-3">
                <span class="px-2 py-1 bg-danger/20 text-danger rounded-full text-sm">设备故障</span>
              </td>
              <td class="border border-border px-4 py-3 text-text-primary">2025-08-02 11:30:45</td>
              <td class="border border-border px-4 py-3 text-text-primary">2025-08-02 15:15:30</td>
              <td class="border border-border px-4 py-3 text-text-primary">3小时44分钟</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-6 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 5 条，每页显示 5 条，第 1 页/共 1 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-2 bg-light border border-border rounded-lg text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-chevron-left"></i>
          </button>
          <button class="px-3 py-2 bg-primary text-white border border-primary rounded-lg">1</button>
          <button class="px-3 py-2 bg-light border border-border rounded-lg text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 设置当前日期为默认值
      const today = new Date();
      const formattedDate = today.toISOString().split('T')[0];
      document.getElementById('start-date').value = formattedDate;
      document.getElementById('end-date').value = formattedDate;

      // 模拟数据
      const mockData = [
        {
          managementUnit: '薛家湾供电公司',
          stationCode: 'TQ001',
          stationName: '赛罕一台区',
          lineCode: 'XL001',
          lineName: '赛罕主线',
          capacity: 500,
          exceptionType: '电压异常',
          startTime: '2025-08-04 08:30:15',
          endTime: '2025-08-04 10:15:30',
          duration: '1小时45分钟'
        },
        {
          managementUnit: '薛家湾供电公司',
          stationCode: 'TQ002',
          stationName: '九原二台区',
          lineCode: 'XL002',
          lineName: '九原支线',
          capacity: 320,
          exceptionType: '设备故障',
          startTime: '2025-08-04 14:20:45',
          endTime: '2025-08-04 16:45:20',
          duration: '2小时24分钟'
        },
        {
          managementUnit: '薛家湾供电公司',
          stationCode: 'TQ003',
          stationName: '康巴什三台区',
          lineCode: 'XL003',
          lineName: '康巴什环线',
          capacity: 750,
          exceptionType: '通信异常',
          startTime: '2025-08-03 22:15:30',
          endTime: '2025-08-04 06:30:15',
          duration: '8小时14分钟'
        },
        {
          managementUnit: '薛家湾供电公司',
          stationCode: 'TQ004',
          stationName: '集宁四台区',
          lineCode: 'XL004',
          lineName: '集宁北线',
          capacity: 420,
          exceptionType: '电压异常',
          startTime: '2025-08-03 16:45:20',
          endTime: '2025-08-03 18:20:10',
          duration: '1小时34分钟'
        },
        {
          managementUnit: '薛家湾供电公司',
          stationCode: 'TQ005',
          stationName: '东胜五台区',
          lineCode: 'XL005',
          lineName: '东胜工业线',
          capacity: 680,
          exceptionType: '设备故障',
          startTime: '2025-08-02 11:30:45',
          endTime: '2025-08-02 15:15:30',
          duration: '3小时44分钟'
        }
      ];

      let filteredData = [...mockData];

      // 查询按钮事件
      document.getElementById('btn-search').addEventListener('click', function () {
        const managementUnit = document.getElementById('management-unit').value;
        const stationCode = document.getElementById('station-code').value;
        const stationName = document.getElementById('station-name').value;
        const exceptionType = document.getElementById('exception-type').value;
        const startDate = document.getElementById('start-date').value;
        const endDate = document.getElementById('end-date').value;

        // 过滤数据
        filteredData = mockData.filter(item => {
          let match = true;

          if (managementUnit && item.managementUnit !== managementUnit) match = false;
          if (stationCode && !item.stationCode.includes(stationCode)) match = false;
          if (stationName && !item.stationName.includes(stationName)) match = false;
          if (exceptionType && item.exceptionType !== exceptionType) match = false;

          // 简单的日期过滤（实际应用中需要更复杂的日期比较）
          if (startDate || endDate) {
            const itemDate = item.startTime.split(' ')[0];
            if (startDate && itemDate < startDate) match = false;
            if (endDate && itemDate > endDate) match = false;
          }

          return match;
        });

        updateTable();
        updateStatistics();
      });

      // 重置按钮事件
      document.getElementById('btn-reset').addEventListener('click', function () {
        document.getElementById('management-unit').value = '';
        document.getElementById('station-code').value = '';
        document.getElementById('station-name').value = '';
        document.getElementById('exception-type').value = '';
        document.getElementById('start-date').value = formattedDate;
        document.getElementById('end-date').value = formattedDate;

        filteredData = [...mockData];
        updateTable();
        updateStatistics();
      });

      // 导出按钮事件
      document.getElementById('btn-export').addEventListener('click', function () {
        console.log('导出数据', filteredData);
        alert('导出功能已触发，实际应用中会下载Excel文件');
      });

      // 更新表格
      function updateTable() {
        const tbody = document.getElementById('data-table-body');
        tbody.innerHTML = '';

        filteredData.forEach(item => {
          const row = document.createElement('tr');
          row.className = 'hover:bg-light/50 transition-colors';

          const exceptionTypeClass = getExceptionTypeClass(item.exceptionType);

          row.innerHTML = `
            <td class="border border-border px-4 py-3 text-text-primary">${item.managementUnit}</td>
            <td class="border border-border px-4 py-3 text-text-primary">${item.stationCode}</td>
            <td class="border border-border px-4 py-3 text-text-primary">${item.stationName}</td>
            <td class="border border-border px-4 py-3 text-text-primary">${item.lineCode}</td>
            <td class="border border-border px-4 py-3 text-text-primary">${item.lineName}</td>
            <td class="border border-border px-4 py-3 text-text-primary">${item.capacity}</td>
            <td class="border border-border px-4 py-3">
              <span class="px-2 py-1 ${exceptionTypeClass} rounded-full text-sm">${item.exceptionType}</span>
            </td>
            <td class="border border-border px-4 py-3 text-text-primary">${item.startTime}</td>
            <td class="border border-border px-4 py-3 text-text-primary">${item.endTime}</td>
            <td class="border border-border px-4 py-3 text-text-primary">${item.duration}</td>
          `;

          tbody.appendChild(row);
        });
      }

      // 更新统计信息
      function updateStatistics() {
        const total = filteredData.length;
        const voltageExceptions = filteredData.filter(item => item.exceptionType === '电压异常').length;
        const deviceFailures = filteredData.filter(item => item.exceptionType === '设备故障').length;
        const commExceptions = filteredData.filter(item => item.exceptionType === '通信异常').length;

        // 更新统计卡片
        const statCards = document.querySelectorAll('.stat-card h3');
        statCards[0].innerHTML = `${total} <span class="text-lg font-normal text-text-secondary">条</span>`;
        statCards[1].innerHTML = `${voltageExceptions} <span class="text-lg font-normal text-text-secondary">条</span>`;
        statCards[2].innerHTML = `${deviceFailures} <span class="text-lg font-normal text-text-secondary">条</span>`;
        statCards[3].innerHTML = `${commExceptions} <span class="text-lg font-normal text-text-secondary">条</span>`;
      }

      // 获取异常类型样式类
      function getExceptionTypeClass(type) {
        switch (type) {
          case '电压异常':
            return 'bg-warning/20 text-warning';
          case '设备故障':
            return 'bg-danger/20 text-danger';
          case '通信异常':
            return 'bg-secondary/20 text-secondary';
          case '电流异常':
            return 'bg-primary/20 text-primary';
          case '功率异常':
            return 'bg-accent/20 text-accent';
          default:
            return 'bg-light/20 text-text-secondary';
        }
      }

      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });
    });
  </script>
</body>

</html>
